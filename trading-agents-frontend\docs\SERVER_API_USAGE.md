# 后端API配置使用指南

本文档说明如何在Next.js API路由中使用专门为后端设计的axios配置，与前端的axios实例区分开。

## 概述

项目中有两套独立的axios配置：

1. **前端axios配置** (`src/lib/api.ts`) - 用于浏览器端的API调用
2. **后端axios配置** (`src/lib/server-api.ts`) - 用于Next.js API路由中的服务器端调用

## 后端axios配置特点

### 配置差异

| 特性 | 前端配置 | 后端配置 |
|------|----------|----------|
| 超时时间 | 30秒 | 10秒 |
| User-Agent | 浏览器默认 | `TradingAgents-NextJS-Server/1.0.0` |
| 请求头 | 基础JSON头 | 包含服务器标识和时间戳 |
| 错误处理 | 用户友好提示 | 详细服务器日志 |
| 内部API密钥 | 无 | 支持 `INTERNAL_API_KEY` |

### 环境变量

```env
# 基础API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# 可选：内部API密钥（用于服务器间通信）
INTERNAL_API_KEY=your_internal_api_key
```

## 使用方法

### 方法1：使用预定义的API方法

```typescript
import { serverApiMethods } from '@/lib/server-api';

export async function GET() {
  try {
    // 健康检查
    const health = await serverApiMethods.healthCheck();
    
    // 获取服务器状态
    const status = await serverApiMethods.getServerStatus();
    
    return NextResponse.json({ health, status });
  } catch (error) {
    // 错误处理
  }
}
```

### 方法2：直接使用axios实例

```typescript
import { serverApi } from '@/lib/server-api';

export async function GET() {
  try {
    // 自定义GET请求
    const response = await serverApi.get('/api/custom-endpoint', {
      timeout: 8000,
      headers: {
        'X-Custom-Header': 'value',
      },
    });
    
    return NextResponse.json(response.data);
  } catch (error) {
    // 错误处理
  }
}
```

### 方法3：POST请求示例

```typescript
import { serverApi } from '@/lib/server-api';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 转发到后端API
    const response = await serverApi.post('/api/data/process', body, {
      headers: {
        'X-Request-Type': 'frontend-proxy',
      },
    });
    
    return NextResponse.json(response.data);
  } catch (error) {
    // 错误处理
  }
}
```

## 拦截器功能

### 请求拦截器

自动添加以下请求头：
- `X-Request-Source: nextjs-server`
- `X-Request-Timestamp: ISO时间戳`
- `X-Internal-API-Key: 内部API密钥`（如果配置）

### 响应拦截器

提供详细的错误日志，包括：
- 请求URL和方法
- 响应状态码和状态文本
- 错误响应数据

## 最佳实践

### 1. 错误处理

```typescript
export async function GET() {
  try {
    const response = await serverApi.get('/api/endpoint');
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('API Error:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
```

### 2. 超时配置

```typescript
// 为特定请求设置不同的超时时间
const response = await serverApi.get('/api/slow-endpoint', {
  timeout: 30000, // 30秒
});
```

### 3. 请求头定制

```typescript
const response = await serverApi.post('/api/endpoint', data, {
  headers: {
    'X-Request-Type': 'batch-process',
    'X-Priority': 'high',
  },
});
```

## 与前端配置的区别

### 不要在API路由中使用前端配置

❌ **错误做法：**
```typescript
import { api } from '@/lib/api'; // 这是前端配置

export async function GET() {
  // 不要在API路由中使用前端的axios实例
  const response = await api.get('/endpoint');
}
```

✅ **正确做法：**
```typescript
import { serverApi } from '@/lib/server-api'; // 后端专用配置

export async function GET() {
  // 在API路由中使用后端专用的axios实例
  const response = await serverApi.get('/endpoint');
}
```

## 调试和监控

后端axios配置包含详细的日志记录，可以通过服务器控制台查看：

```
Server API Response Error: {
  url: '/api/endpoint',
  method: 'GET',
  status: 500,
  statusText: 'Internal Server Error',
  data: { error: 'Database connection failed' }
}
```

## 扩展配置

如需添加新的API方法，可以在 `serverApiMethods` 对象中添加：

```typescript
// 在 src/lib/server-api.ts 中添加
export const serverApiMethods = {
  // 现有方法...
  
  // 新增方法
  customMethod: async (param: string): Promise<any> => {
    try {
      const response = await serverApi.get(`/api/custom/${param}`);
      return response.data;
    } catch (error) {
      console.error('Custom method failed:', error);
      throw error;
    }
  },
};
```
